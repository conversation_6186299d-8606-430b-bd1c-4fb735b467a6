/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Body Styling */
body {
    font-family: 'Poppins', sans-serif;
    background: #f4f4f4;
    color: #333;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 50px;
    background: #1e293b; /* Navy blue background */
    color: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1000;
}

/* Logo */
.logo {
    font-size: 1.8rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-right: 20px;
}

/* Search Bar */
.search-bar {
    flex: 1; /* Takes up available space */
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 0 20px; /* Adds spacing on both sides */
    position: relative;
}

.search-bar input {
    flex: 1; /* Fills remaining space */
    padding: 8px 12px;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 5px;
    outline: none;
    transition: all 0.3s ease;
}

.search-bar input:focus {
    border-color: #1e293b; /* Matches the primary color */
    box-shadow: 0 0 5px rgba(30, 41, 59, 0.5); /* Subtle glow effect */
}

.search-bar button {
    padding: 8px 12px;
    font-size: 1rem;
    background-color: #1e293b;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-bar button:hover {
    background-color: #0f172a; /* Darker blue for hover */
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 40px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1001;
}

.suggestion-item {
    padding: 10px 15px;
    cursor: pointer;
    color: #333;
    border-bottom: 1px solid #eee;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-right: 20px;
}

.cart-icon, .wishlist-icon, .user-icon {
    position: relative;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.cart-icon:hover, .wishlist-icon:hover, .user-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.cart-count, .wishlist-count {
    position: absolute;
    top: 0;
    right: 0;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

/* User Menu */
.user-menu {
    position: absolute;
    top: 100%;
    right: 50px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1001;
    min-width: 150px;
}

.user-menu.show {
    display: block;
}

.user-menu-item {
    padding: 12px 16px;
    cursor: pointer;
    color: #333;
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s ease;
}

.user-menu-item:hover {
    background-color: #f8f9fa;
}

.user-menu-item:last-child {
    border-bottom: none;
}

/* Navigation Links */
nav ul {
    display: flex;
    list-style: none;
    gap: 20px;
}

nav ul li a {
    text-decoration: none;
    color: white;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: #cbd5e1;
}

/* Cart Sidebar */
.cart-sidebar, .wishlist-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -4px 0 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1002;
    display: flex;
    flex-direction: column;
}

.cart-sidebar.open, .wishlist-sidebar.open {
    right: 0;
}

.cart-header, .wishlist-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1e293b;
    color: white;
}

.close-cart, .close-wishlist {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: white;
    padding: 5px;
}

.cart-items, .wishlist-items {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.cart-item, .wishlist-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    align-items: center;
}

.cart-item img, .wishlist-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
}

.item-details p {
    margin: 2px 0;
    font-size: 0.9rem;
    color: #666;
}

.item-price {
    font-weight: bold;
    color: #1e293b !important;
}

.remove-item, .remove-wishlist-item {
    background: #e74c3c;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
}

.cart-total {
    font-size: 1.2rem;
    margin-bottom: 15px;
    text-align: center;
}

.checkout-btn, .add-to-cart-from-wishlist {
    width: 100%;
    padding: 15px;
    background: #1e293b;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.checkout-btn:hover, .add-to-cart-from-wishlist:hover {
    background: #0f172a;
}

.empty-cart, .empty-wishlist {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
}

/* Product Container */
.product-container {
    display: flex;
    gap: 40px;
    padding: 50px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Product Gallery */
.product-gallery {
    flex: 1;
}

.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.product-gallery img {
    width: 100%;
    border-radius: 10px;
    cursor: zoom-in;
    transition: transform 0.3s ease;
}

.product-gallery img:hover {
    transform: scale(1.05);
}

.wishlist-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    font-size: 20px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wishlist-btn:hover {
    background: white;
    transform: scale(1.1);
}

.image-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.sale-badge {
    background: #e74c3c;
    color: white;
}

.new-badge {
    background: #27ae60;
    color: white;
}

.thumbnails {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.thumbnails img {
    width: 80px;
    cursor: pointer;
    border-radius: 5px;
    transition: transform 0.3s ease, border 0.3s ease;
    border: 2px solid transparent;
}

.thumbnails img:hover {
    transform: scale(1.1);
    border: 2px solid #1e293b;
}

.thumbnails img.active {
    border: 2px solid #1e293b;
}

/* Product Details */
.product-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.product-details h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.brand {
    font-size: 1rem;
    text-transform: uppercase;
    color: #1e293b;
    margin-bottom: 10px;
}

.availability {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.stock-status.in-stock {
    color: #27ae60;
    font-weight: bold;
}

.sku {
    color: #666;
}

.price {
    font-size: 1.3rem;
    margin-bottom: 20px;
}

.current-price {
    color: #1e293b;
    font-weight: bold;
}

.old-price {
    text-decoration: line-through;
    margin-left: 10px;
    color: #aaa;
}

.discount {
    color: #1e293b;
    margin-left: 10px;
}

.description {
    font-size: 1rem;
    color: #555;
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Ratings Section */
.ratings {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.ratings:hover {
    opacity: 0.8;
}

.stars {
    display: flex;
    gap: 2px;
    color: #ffcc00; /* Gold for stars */
}

.ratings p {
    font-size: 0.9rem;
    color: #555;
}

.view-reviews {
    color: #1e293b;
    text-decoration: underline;
    font-weight: bold;
}

/* Product Variants */
.product-variants {
    margin-bottom: 25px;
}

.variant-group {
    margin-bottom: 20px;
}

.variant-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.size-options, .band-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.size-option, .band-option {
    padding: 10px 15px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.size-option:hover, .band-option:hover {
    border-color: #1e293b;
}

.size-option.active, .band-option.active {
    border-color: #1e293b;
    background: #1e293b;
    color: white;
}

.color-options {
    display: flex;
    gap: 10px;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-option:hover {
    border-color: #1e293b;
    transform: scale(1.1);
}

.color-option.active {
    border-color: #1e293b;
    box-shadow: 0 0 0 2px white, 0 0 0 4px #1e293b;
}

/* Quantity Selector */
.quantity-selector {
    margin-bottom: 25px;
}

.quantity-selector label {
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0;
    width: fit-content;
    border: 2px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.quantity-controls button {
    background-color: #1e293b;
    color: white;
    border: none;
    padding: 12px 15px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.quantity-controls button:hover {
    background-color: #0f172a;
}

.quantity-controls input {
    width: 60px;
    text-align: center;
    font-size: 1rem;
    border: none;
    padding: 12px 5px;
    outline: none;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.add-to-cart, .buy-now {
    flex: 1;
    padding: 15px;
    font-size: 1.1rem;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.add-to-cart {
    background-color: #1e293b;
    color: white;
}

.add-to-cart:hover {
    background-color: #0f172a;
    transform: translateY(-2px);
}

.buy-now {
    background-color: #e74c3c;
    color: white;
}

.buy-now:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
}

/* Product Features */
.product-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #555;
}

.feature-icon {
    font-size: 1.2rem;
}

/* Add to Cart Button */
.add-to-cart {
    background-color: #1e293b;
    color: white;
    border: none;
    padding: 15px;
    font-size: 1.2rem;
    cursor: pointer;
    border-radius: 10px;
    margin-bottom: 20px;
}

.add-to-cart:hover {
    background-color: #0f172a;
}

/* Promotional Section */
.promotion {
    margin-top: 20px;
    padding: 20px;
    background: #f4f4f4;
    border: 1px solid #ddd;
    border-radius: 10px;
    text-align: center;
}

.promotion h3 {
    font-size: 1.2rem;
    color: #1e293b; /* Primary color */
    margin-bottom: 10px;
}

.promotion p {
    font-size: 1rem;
    color: #555;
    margin-bottom: 10px;
}

.promo-btn {
    padding: 10px 20px;
    background-color: #1e293b;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.promo-btn:hover {
    background-color: #0f172a; /* Darker blue */
}

/* Modals */
.modal, .image-modal {
    display: none;
    position: fixed;
    z-index: 1003;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #000;
}

.modal-content h2 {
    margin-bottom: 20px;
    color: #1e293b;
}

.modal-content form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.modal-content input, .modal-content textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.modal-content button {
    padding: 12px;
    background: #1e293b;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.modal-content button:hover {
    background: #0f172a;
}

/* Image Modal */
.image-modal .modal-content {
    max-width: 90%;
    max-height: 90%;
    padding: 20px;
}

.image-modal img {
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 10px;
}

.modal-thumbnails {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
}

.modal-thumbnails img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.modal-thumbnails img:hover {
    border-color: #1e293b;
}

/* Star Rating Input */
.rating-input {
    margin-bottom: 15px;
}

.star-rating {
    display: flex;
    gap: 5px;
    font-size: 24px;
    margin-top: 5px;
}

.star-input {
    cursor: pointer;
    color: #ddd;
    transition: color 0.3s ease;
}

.star-input:hover {
    color: #ffcc00;
}

/* Related Products */
.related-products {
    padding: 50px;
    background: #f8f9fa;
}

.related-products h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #1e293b;
    font-size: 2rem;
}

.products-carousel {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    overflow: hidden;
}

.products-container {
    display: flex;
    transition: transform 0.3s ease;
}

.product-card {
    flex: 0 0 calc(33.333% - 20px);
    margin: 0 10px;
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 15px;
}

.product-card h3 {
    margin-bottom: 10px;
    color: #333;
}

.product-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 15px;
}

.quick-add {
    padding: 10px 20px;
    background: #1e293b;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.quick-add:hover {
    background: #0f172a;
}

.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: #1e293b;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.carousel-btn:hover {
    background: #0f172a;
}

.carousel-btn.prev {
    left: -25px;
}

.carousel-btn.next {
    right: -25px;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    background: #1e293b;
    color: white;
    margin-top: 50px;
}
/* Reviews Section */
.reviews-section {
    padding: 50px;
    max-width: 1200px;
    margin: 0 auto;
}

.reviews-section h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #1e293b;
    font-size: 2rem;
}

.reviews-summary {
    display: flex;
    gap: 40px;
    margin-bottom: 30px;
    padding: 30px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.rating-overview {
    text-align: center;
    flex: 1;
}

.average-rating {
    font-size: 3rem;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 10px;
}

.stars-large {
    font-size: 24px;
    color: #ffcc00;
    margin-bottom: 10px;
}

.rating-breakdown {
    flex: 2;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.rating-bar span:first-child {
    width: 30px;
    font-size: 0.9rem;
}

.rating-bar span:last-child {
    width: 40px;
    font-size: 0.9rem;
    text-align: right;
}

.bar {
    flex: 1;
    height: 8px;
    background: #eee;
    border-radius: 4px;
    overflow: hidden;
}

.fill {
    height: 100%;
    background: #ffcc00;
    transition: width 0.3s ease;
}

.review-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.write-review-btn {
    padding: 12px 24px;
    background: #1e293b;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.write-review-btn:hover {
    background: #0f172a;
}

.review-filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.review-item {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.review-stars {
    color: #ffcc00;
    font-size: 16px;
}

.review-date {
    color: #666;
    font-size: 0.9rem;
}

.review-text {
    line-height: 1.6;
    margin-bottom: 15px;
    color: #333;
}

.review-helpful button {
    background: none;
    border: 1px solid #ddd;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.review-helpful button:hover {
    background: #f8f9fa;
    border-color: #1e293b;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    z-index: 1004;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #27ae60;
}

.toast.error {
    background: #e74c3c;
}

.toast.info {
    background: #3498db;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: 15px;
        padding: 15px 20px;
    }

    .search-bar {
        order: 3;
        width: 100%;
        margin: 0;
    }

    .header-actions {
        order: 2;
        margin: 0;
    }

    nav {
        order: 1;
    }

    nav ul {
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .product-container {
        flex-direction: column;
        padding: 20px;
        gap: 20px;
    }

    .cart-sidebar, .wishlist-sidebar {
        width: 100%;
        right: -100%;
    }

    .products-carousel {
        padding: 0 20px;
    }

    .product-card {
        flex: 0 0 calc(100% - 20px);
    }

    .carousel-btn {
        display: none;
    }

    .reviews-summary {
        flex-direction: column;
        gap: 20px;
    }

    .review-actions {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .action-buttons {
        flex-direction: column;
    }

    .product-features {
        grid-template-columns: 1fr;
    }
}
