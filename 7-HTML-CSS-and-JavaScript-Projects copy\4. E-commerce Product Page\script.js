// Global variables
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
let currentProduct = {
    id: 'elw-001',
    name: 'Elegant Luxury Watch',
    basePrice: 499.99,
    size: '42mm',
    band: 'leather',
    color: 'black',
    image: 'https://ng.jumia.is/unsafe/fit-in/500x500/filters:fill(white)/product/26/1542852/1.jpg?5491'
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();
    updateWishlistCount();
    initializeProductVariants();
    initializeSearch();
    updatePrice();
});

// Product image functions
function changeImage(imageSrc, element) {
    document.getElementById('main-image').src = imageSrc;

    // Update active thumbnail
    const thumbnails = document.querySelectorAll('.thumbnails img');
    thumbnails.forEach(thumb => thumb.classList.remove('active'));
    if (element) {
        element.classList.add('active');
    }
}

// Quantity functions
function increaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    if (currentValue < 10) {
        quantityInput.value = currentValue + 1;
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
    }
}

// Product variants
function initializeProductVariants() {
    // Size options
    const sizeOptions = document.querySelectorAll('.size-option');
    sizeOptions.forEach(option => {
        option.addEventListener('click', function() {
            sizeOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            currentProduct.size = this.dataset.size;
            updatePrice();
        });
    });

    // Band options
    const bandOptions = document.querySelectorAll('.band-option');
    bandOptions.forEach(option => {
        option.addEventListener('click', function() {
            bandOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            currentProduct.band = this.dataset.band;
            updatePrice();
        });
    });

    // Color options
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            colorOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            currentProduct.color = this.dataset.color;
        });
    });
}

function updatePrice() {
    const sizePrice = parseFloat(document.querySelector('.size-option.active').dataset.price);
    const bandPrice = parseFloat(document.querySelector('.band-option.active').dataset.price);
    const totalPrice = sizePrice + bandPrice;

    document.getElementById('current-price').textContent = `$${totalPrice.toFixed(2)}`;
    currentProduct.basePrice = totalPrice;
}

// Shopping Cart Functions
function addToCart() {
    const quantity = parseInt(document.getElementById('quantity').value);
    const product = {
        ...currentProduct,
        quantity: quantity,
        totalPrice: currentProduct.basePrice * quantity
    };

    const existingItemIndex = cart.findIndex(item =>
        item.id === product.id &&
        item.size === product.size &&
        item.band === product.band &&
        item.color === product.color
    );

    if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += quantity;
        cart[existingItemIndex].totalPrice = cart[existingItemIndex].quantity * cart[existingItemIndex].basePrice;
    } else {
        cart.push(product);
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    updateCartDisplay();
    showToast('Product added to cart!', 'success');
}

function removeFromCart(index) {
    cart.splice(index, 1);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    updateCartDisplay();
    showToast('Product removed from cart!', 'info');
}

function updateCartCount() {
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    document.getElementById('cart-count').textContent = totalItems;
}

function updateCartDisplay() {
    const cartItems = document.getElementById('cart-items');
    const cartTotal = document.getElementById('cart-total');

    if (cart.length === 0) {
        cartItems.innerHTML = '<p class="empty-cart">Your cart is empty</p>';
        cartTotal.textContent = '0.00';
        return;
    }

    let total = 0;
    cartItems.innerHTML = cart.map((item, index) => {
        total += item.totalPrice;
        return `
            <div class="cart-item">
                <img src="${item.image}" alt="${item.name}">
                <div class="item-details">
                    <h4>${item.name}</h4>
                    <p>Size: ${item.size}, Band: ${item.band}</p>
                    <p>Quantity: ${item.quantity}</p>
                    <p class="item-price">$${item.totalPrice.toFixed(2)}</p>
                </div>
                <button class="remove-item" onclick="removeFromCart(${index})">×</button>
            </div>
        `;
    }).join('');

    cartTotal.textContent = total.toFixed(2);
}

function toggleCart() {
    const cartSidebar = document.getElementById('cart-sidebar');
    cartSidebar.classList.toggle('open');
    updateCartDisplay();
}

function buyNow() {
    addToCart();
    showToast('Redirecting to checkout...', 'success');
    setTimeout(() => {
        alert('This would redirect to checkout page in a real application!');
    }, 1000);
}

function checkout() {
    if (cart.length === 0) {
        showToast('Your cart is empty!', 'error');
        return;
    }
    showToast('Redirecting to checkout...', 'success');
    setTimeout(() => {
        alert('This would redirect to checkout page in a real application!');
    }, 1000);
}

// Wishlist Functions
function toggleWishlistItem() {
    const productId = currentProduct.id;
    const existingIndex = wishlist.findIndex(item => item.id === productId);

    if (existingIndex > -1) {
        wishlist.splice(existingIndex, 1);
        document.getElementById('wishlist-heart').textContent = '🤍';
        showToast('Removed from wishlist!', 'info');
    } else {
        wishlist.push({...currentProduct});
        document.getElementById('wishlist-heart').textContent = '❤️';
        showToast('Added to wishlist!', 'success');
    }

    localStorage.setItem('wishlist', JSON.stringify(wishlist));
    updateWishlistCount();
    updateWishlistDisplay();
}

function updateWishlistCount() {
    document.getElementById('wishlist-count').textContent = wishlist.length;

    // Update heart icon based on current product
    const isInWishlist = wishlist.some(item => item.id === currentProduct.id);
    document.getElementById('wishlist-heart').textContent = isInWishlist ? '❤️' : '🤍';
}

function updateWishlistDisplay() {
    const wishlistItems = document.getElementById('wishlist-items');

    if (wishlist.length === 0) {
        wishlistItems.innerHTML = '<p class="empty-wishlist">Your wishlist is empty</p>';
        return;
    }

    wishlistItems.innerHTML = wishlist.map((item, index) => `
        <div class="wishlist-item">
            <img src="${item.image}" alt="${item.name}">
            <div class="item-details">
                <h4>${item.name}</h4>
                <p class="item-price">$${item.basePrice.toFixed(2)}</p>
                <button class="add-to-cart-from-wishlist" onclick="addWishlistItemToCart(${index})">Add to Cart</button>
            </div>
            <button class="remove-wishlist-item" onclick="removeFromWishlist(${index})">×</button>
        </div>
    `).join('');
}

function removeFromWishlist(index) {
    wishlist.splice(index, 1);
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
    updateWishlistCount();
    updateWishlistDisplay();
    showToast('Removed from wishlist!', 'info');
}

function addWishlistItemToCart(index) {
    const item = wishlist[index];
    const cartItem = {
        ...item,
        quantity: 1,
        totalPrice: item.basePrice
    };

    cart.push(cartItem);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    showToast('Added to cart from wishlist!', 'success');
}

function toggleWishlist() {
    const wishlistSidebar = document.getElementById('wishlist-sidebar');
    wishlistSidebar.classList.toggle('open');
    updateWishlistDisplay();
}

// Search Functions
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    const suggestions = document.getElementById('search-suggestions');

    const searchData = [
        'Luxury Watch', 'Sport Watch', 'Digital Watch', 'Classic Watch',
        'Gold Watch', 'Silver Watch', 'Leather Band', 'Steel Band',
        'Chronograph', 'Automatic', 'Quartz', 'Waterproof'
    ];

    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        if (query.length < 2) {
            suggestions.style.display = 'none';
            return;
        }

        const matches = searchData.filter(item =>
            item.toLowerCase().includes(query)
        );

        if (matches.length > 0) {
            suggestions.innerHTML = matches.map(match =>
                `<div class="suggestion-item" onclick="selectSuggestion('${match}')">${match}</div>`
            ).join('');
            suggestions.style.display = 'block';
        } else {
            suggestions.style.display = 'none';
        }
    });

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestions.contains(e.target)) {
            suggestions.style.display = 'none';
        }
    });
}

function selectSuggestion(suggestion) {
    document.getElementById('search-input').value = suggestion;
    document.getElementById('search-suggestions').style.display = 'none';
    performSearch();
}

function performSearch() {
    const query = document.getElementById('search-input').value;
    showToast(`Searching for: ${query}`, 'info');
    // In a real application, this would perform actual search
}

// Modal Functions
function showLoginModal() {
    document.getElementById('login-modal').style.display = 'block';
    closeUserMenu();
}

function showSignupModal() {
    document.getElementById('signup-modal').style.display = 'block';
    closeUserMenu();
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function toggleUserMenu() {
    const userMenu = document.getElementById('user-menu');
    userMenu.classList.toggle('show');
}

function closeUserMenu() {
    document.getElementById('user-menu').classList.remove('show');
}
// Image Modal Functions
function openImageModal() {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const mainImage = document.getElementById('main-image');

    modalImage.src = mainImage.src;
    modal.style.display = 'block';
}

function closeImageModal() {
    document.getElementById('image-modal').style.display = 'none';
}

function changeModalImage(imageSrc) {
    document.getElementById('modal-image').src = imageSrc;
}

// Reviews Functions
function scrollToReviews() {
    document.getElementById('reviews-section').scrollIntoView({ behavior: 'smooth' });
}

function showReviewModal() {
    document.getElementById('review-modal').style.display = 'block';
}

function markHelpful(reviewId) {
    showToast('Thank you for your feedback!', 'success');
}

// Star rating for reviews
document.addEventListener('DOMContentLoaded', function() {
    const starInputs = document.querySelectorAll('.star-input');
    let selectedRating = 0;

    starInputs.forEach((star, index) => {
        star.addEventListener('click', function() {
            selectedRating = index + 1;
            updateStarDisplay(selectedRating);
        });

        star.addEventListener('mouseover', function() {
            updateStarDisplay(index + 1);
        });
    });

    document.querySelector('.star-rating').addEventListener('mouseleave', function() {
        updateStarDisplay(selectedRating);
    });

    function updateStarDisplay(rating) {
        starInputs.forEach((star, index) => {
            star.textContent = index < rating ? '★' : '☆';
        });
    }
});

// Products Carousel
let currentSlide = 0;
const productsPerView = 3;

function slideProducts(direction) {
    const container = document.getElementById('products-container');
    const products = container.children;
    const maxSlide = Math.max(0, products.length - productsPerView);

    currentSlide += direction;

    if (currentSlide < 0) currentSlide = 0;
    if (currentSlide > maxSlide) currentSlide = maxSlide;

    const translateX = -(currentSlide * (100 / productsPerView));
    container.style.transform = `translateX(${translateX}%)`;
}

function quickAdd(productId) {
    showToast(`${productId} added to cart!`, 'success');
    // In a real application, this would add the specific product to cart
}

// Notification Toast
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type} show`;

    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// Newsletter signup
function showNewsletterSignup() {
    const email = prompt('Enter your email address:');
    if (email && email.includes('@')) {
        showToast('Thank you for subscribing to our newsletter!', 'success');
    } else if (email) {
        showToast('Please enter a valid email address.', 'error');
    }
}

// Form submissions
document.addEventListener('DOMContentLoaded', function() {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            showToast('Login functionality would be implemented here!', 'info');
            closeModal('login-modal');
        });
    }

    // Signup form
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', function(e) {
            e.preventDefault();
            showToast('Signup functionality would be implemented here!', 'info');
            closeModal('signup-modal');
        });
    }

    // Review form
    const reviewForm = document.getElementById('review-form');
    if (reviewForm) {
        reviewForm.addEventListener('submit', function(e) {
            e.preventDefault();
            showToast('Review submitted successfully!', 'success');
            closeModal('review-modal');
        });
    }
});

// Close modals when clicking outside
window.addEventListener('click', function(e) {
    const modals = document.querySelectorAll('.modal, .image-modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
});

// Update thumbnail click handlers in HTML
document.addEventListener('DOMContentLoaded', function() {
    const thumbnails = document.querySelectorAll('.thumbnails img');
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            changeImage(this.src, this);
        });
    });
});
